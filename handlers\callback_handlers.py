import logging
import pytz
from datetime import datetime, timedelta
from urllib.parse import quote
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, Update
from telegram.error import BadRequest
from telegram.ext import ContextTypes
from database import get_db_connection, get_user_balance, add_subscription_history, process_subscription_referral_bonus, get_total_referral_earnings
from remnawave_utils import user_exists, activate_trial_subscription, extend_remnawave_subscription
from utils import escape_markdown_v2, format_date, check_channel_subscription
from config import REQUIRED_CHANNEL_ID
from handlers.states import user_states, payment_messages
from handlers.keyboard_utils import get_main_keyboard
from handlers.payment_handlers import check_payment_delayed

async def button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id

    if query.data == 'trial':
        await query.message.edit_caption("🎁 Пробная подписка на 5 дней.\n\nНажмите для активации:", reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("✅ Активировать", callback_data='activate_trial')],
            [InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]
        ]))

    elif query.data == 'back_to_main':
        # Сбрасываем состояние ожидания ввода суммы платежа, если оно было
        if user_id in user_states and user_states[user_id].startswith("waiting_for_payment_amount_"):
            del user_states[user_id]

        await query.message.edit_caption(
            "👋 *Добро пожаловать в SPACE VPN\\!*\n\n"
            "*Наши локации:*\n"
            "🇫🇮 Финляндия\n"
            "🇩🇪 Германия\n\n"
            ">*Ошибка в боте? Нашел баг?*\n"
            ">Пиши: @spacevpn\\_help\n\n"
            "👇 *Выберите опцию:*",
            reply_markup=get_main_keyboard(user_id),
            parse_mode='MarkdownV2'
        )

    elif query.data == 'buy':
        balance = await get_user_balance(user_id)
        await query.message.edit_caption(
            "📊 *Цены на тарифы*\n\n"
            "— *1 месяц* 100₽\n"
            "— *3 месяца* 250₽\n"
            "— *1 год* 850₽\n\n"
            f"Ваш баланс: `{balance:.2f}₽`",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("➡️ Продолжить покупку", callback_data='buy_continue')],
                [InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]
            ]),
            parse_mode='Markdown'
        )

    elif query.data == 'buy_continue':
        balance = await get_user_balance(user_id)
        if balance < 100:
            await query.message.edit_caption(
                "⚠️ *Внимание*\n\n"
                "Для покупки подписки необходимо иметь на балансе минимум 100₽\n"
                f"Ваш текущий баланс: `{balance:.2f}₽`\n\n"
                "Пожалуйста, пополните баланс:",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("💳 Пополнение", callback_data='payment')],
                    [InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]
                ]),
                parse_mode='Markdown'
            )
        else:
            await query.message.edit_caption(
                "💰 *Тарифы:*\n\n— Безлимитный трафик\n— Высокая скорость (от 500 мбит/с)\n— Поддержка 24/7\n\nВыберите тариф:",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("1 месяц - 100₽", callback_data='buy_1month')],
                    [InlineKeyboardButton("3 месяца - 250₽", callback_data='buy_3months')],
                    [InlineKeyboardButton("1 год - 850₽", callback_data='buy_12months')],
                    [InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]
                ]),
                parse_mode='Markdown'
            )

    elif query.data.startswith('buy_'):
        period = query.data.split('_')[1]
        prices = {'1month': 100, '3months': 250, '12months': 850}
        periods = {'1month': '1 месяц', '3months': '3 месяца', '12months': '1 год'}
        period_days = {'1month': 30, '3months': 90, '12months': 365}

        # Получаем существующий marzban_username из базы данных
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT username, marzban_username FROM users WHERE user_id = ?', (user_id,))
            result = c.fetchone()
            username, marzban_username = result if result else (None, None)

        # Если marzban_username не задан, используем user_<user_id>
        if not marzban_username:
            marzban_username = f"user_{user_id}"
            logging.info(f"No marzban_username found for user_id {user_id}, using {marzban_username}")

        balance = await get_user_balance(user_id)

        if balance < prices[period]:
            await query.message.edit_caption(
                "❌ Недостаточно средств.",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("« Назад", callback_data='buy')]])
            )
            return

        success, result, user_info = await extend_remnawave_subscription(user_id, marzban_username, period_days[period])
        if success:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute('UPDATE users SET balance = balance - ?, subscription_active = 1, subscription_end_date = ?, expiry_notified = 0, marzban_username = ? WHERE user_id = ?',
                        (prices[period], result.isoformat(), marzban_username, user_id))
                conn.commit()

            # Добавляем запись в историю подписок (убираем await, так как функция синхронная)
            add_subscription_history(user_id, periods[period], prices[period], result)

            # Обрабатываем реферальный бонус за покупку подписки
            success_referral, referrer_id, bonus_amount = process_subscription_referral_bonus(user_id, prices[period])
            if success_referral:
                try:
                    await context.bot.send_message(
                        referrer_id,
                        f"🎉 *Реферальный бонус\\!*\n\n"
                        f"Ваш реферал купил подписку\\!\n"
                        f"Бонус: `{bonus_amount:.2f}₽` \\(20% от покупки\\)\n"
                        f"Сумма покупки: `{prices[period]}₽`",
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                        ]),
                        parse_mode='MarkdownV2'
                    )
                except Exception as e:
                    logging.error(f"Failed to notify referrer {referrer_id} about subscription bonus: {e}")

            keyboard = [
                [
                    InlineKeyboardButton("📋 Моя подписка", callback_data='show_subscription')
                ],
                [
                    InlineKeyboardButton("« Главное меню", callback_data='back_to_main')
                ]
            ]

            text = (
                f"✅ *Подписка оплачена!*\n\n"
                f"Период: `{periods[period]}`\n"
                f"Цена: `{prices[period]}₽`\n"
                f"До: `{result.strftime('%d.%m.%Y %H:%M')}`\n\n"
                f"🔗 Ссылка на подписку: `{user_info.subscription_url}`\n\n"
                f"📁 Инструкции для подключения доступны в разделе «Моя подписка»\n\n"
            )
            await query.message.edit_caption(
                text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='Markdown'
            )
        else:
            await query.message.edit_caption(
                f"❌ Ошибка: {result}",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("« Назад", callback_data='buy')]])
            )

    elif query.data == 'payment':
        # Сбрасываем состояние ожидания ввода суммы платежа, если оно было
        if user_id in user_states and user_states[user_id].startswith("waiting_for_payment_amount_"):
            del user_states[user_id]

        await query.message.edit_caption("💳 *Пополнение*\n\nЕсли вы не нашли нужный способ пополнения, обратитесь к @spacevpn\\_help\n\nВыберите способ:", reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("СБП (Система быстрых платежей)", callback_data='payment_sbp')],
            [InlineKeyboardButton("Банковская карта", callback_data='payment_card')],
            [InlineKeyboardButton("Криптовалюта (Cryptobot)", callback_data='payment_crypto')],
            [InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]
        ]), parse_mode='Markdown')

    elif query.data == 'payment_sbp':
        # Сбрасываем состояние ожидания ввода суммы платежа, если оно было
        if user_id in user_states and user_states[user_id].startswith("waiting_for_payment_amount_"):
            del user_states[user_id]

        await query.message.edit_caption("💳 *СБП (Система быстрых платежей)*\n\nВременно пополнение через Сбер недопустно. Выберите другой банк для пополнения.\n\nВведите сумму ответным сообщением (мин. 50₽):", reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("« Назад", callback_data='payment')]]), parse_mode='Markdown')
        user_states[user_id] = "waiting_for_payment_amount_sbp"

    elif query.data == 'payment_card':
        # Сбрасываем состояние ожидания ввода суммы платежа, если оно было
        if user_id in user_states and user_states[user_id].startswith("waiting_for_payment_amount_"):
            del user_states[user_id]

        await query.message.edit_caption("💳 *Банковская карта*\n\nВведите сумму ответным сообщением (мин. 10₽):", reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("« Назад", callback_data='payment')]]), parse_mode='Markdown')
        user_states[user_id] = "waiting_for_payment_amount_card"

    elif query.data == 'payment_crypto':
        # Сбрасываем состояние ожидания ввода суммы платежа, если оно было
        if user_id in user_states and user_states[user_id].startswith("waiting_for_payment_amount_"):
            del user_states[user_id]

        await query.message.edit_caption("💳 *CryptoBot*\n\nКурс: 1 USDT = 90₽\nВведите сумму ответным сообщением (мин. 1 USDT):", reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("« Назад", callback_data='payment')]]), parse_mode='Markdown')
        user_states[user_id] = "waiting_for_payment_amount_crypto"

    elif query.data == 'payment_heleket':
        # Сбрасываем состояние ожидания ввода суммы платежа, если оно было
        if user_id in user_states and user_states[user_id].startswith("waiting_for_payment_amount_"):
            del user_states[user_id]

        await query.message.edit_caption("💳 *Heleket*\n\nВНИМАНИЕ! Баланс может не пополниться из-за некоторых проблем независящих от нас, напишите в поддержку если оплатили @spacevpn\\_help\n\nКурс: 1 USDT = 90₽\nВведите сумму ответным сообщением (мин. 1 USDT):", reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("« Назад", callback_data='payment')]]), parse_mode='Markdown')
        user_states[user_id] = "waiting_for_payment_amount_heleket"

    elif query.data == 'activate_trial':
        # Проверяем подписку на канал
        is_subscribed = await check_channel_subscription(context.bot, user_id, REQUIRED_CHANNEL_ID)
        if not is_subscribed:
            await query.message.edit_caption(
                "❌ *Для получения пробной подписки необходимо подписаться на наш канал*\n\n"
                "Подпишитесь на канал @spaacevpn и попробуйте снова",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("📢 Подписаться на канал", url="https://t.me/spaacevpn")],
                    [InlineKeyboardButton("✅ Я подписался, проверить", callback_data='check_subscription')],
                    [InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]
                ]),
                parse_mode='Markdown'
            )
            return

        # Получаем существующий marzban_username
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT marzban_username FROM users WHERE user_id = ?', (user_id,))
            result = c.fetchone()
            marzban_username = result[0] if result and result[0] else f"user_{user_id}"

        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT trial_end_date FROM users WHERE user_id = ?', (user_id,))
            if c.fetchone()[0]:
                await query.message.edit_caption(
                    "❌ Пробный период уже использован.",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]])
                )
                return
            # Токен больше не нужен, так как он передается при инициализации SDK
            user_info = await user_exists(marzban_username)
            if user_info and user_info.expire_at and user_info.expire_at > datetime.now(pytz.UTC):
                await query.message.edit_caption(
                    "❌ У вас активная подписка.",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]])
                )
                return
            added_user = await activate_trial_subscription(user_id, marzban_username)
            msk_tz = pytz.timezone('Europe/Moscow')
            trial_end = datetime.now(msk_tz) + timedelta(days=5)
            c.execute('UPDATE users SET trial_active = 1, trial_end_date = ?, marzban_username = ? WHERE user_id = ?', (trial_end.isoformat(), marzban_username, user_id))
            conn.commit()

        keyboard = [
            [
                InlineKeyboardButton("📋 Моя подписка", callback_data='show_subscription')
            ],
            [
                InlineKeyboardButton("« Главное меню", callback_data='back_to_main')
            ]
        ]
        await query.message.edit_caption(
            f"✅ *Пробный период активирован!*\n\n"
            f"🔗 Ссылка на подписку: `{added_user.subscription_url}`\n\n"
            f"📁 Инструкции для подключения доступны в разделе «Моя подписка»\n\n",
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='Markdown'
        )

    elif query.data in ('profile', 'profile_refresh'):
        # Получаем существующий marzban_username
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT username, marzban_username FROM users WHERE user_id = ?', (user_id,))
            result = c.fetchone()
            username, marzban_username = result if result else (None, f"user_{user_id}")

        balance = await get_user_balance(user_id)
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT COUNT(*) FROM users WHERE referred_by = ?', (user_id,))
            referral_count = c.fetchone()[0]
            c.execute('SELECT referral_code FROM users WHERE user_id = ?', (user_id,))
            ref_code = c.fetchone()[0]

        # Формируем текст профиля
        profile_text = (
            f"👤 *Профиль*\n\n"
            f"— ID: `{user_id}`\n"
            f"— Баланс: `{balance:.2f}₽`\n"
            f"— Рефералы: `{referral_count}`\n"
        )

        # Формируем клавиатуру
        keyboard = [
            [InlineKeyboardButton("📋 Моя подписка", callback_data='show_subscription')],
            [
                InlineKeyboardButton("💳 Пополнение", callback_data='payment_from_profile'),
                InlineKeyboardButton("📜 История", callback_data='purchase_history')
            ],
            [InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]
        ]

        await query.message.edit_caption(
            profile_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='MarkdownV2'
        )

    elif query.data == 'payment_from_profile':
        await query.message.edit_caption(
            "💳 *Пополнение*\n\nЕсли вы не нашли нужный способ пополнения, обратитесь к @spacevpn\\_help\n\nВыберите способ:",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("СБП (Система быстрых платежей)", callback_data='payment_sbp')],
                [InlineKeyboardButton("Банковская карта", callback_data='payment_card')],
                [InlineKeyboardButton("Криптовалюта (Cryptobot)", callback_data='payment_crypto')],
                [InlineKeyboardButton("« Назад в профиль", callback_data='profile')]
            ]),
            parse_mode='Markdown'
        )

    elif query.data == 'show_subscription':
        # Получаем данные пользователя из базы
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT username, marzban_username, subscription_active, subscription_end_date, trial_active, trial_end_date FROM users WHERE user_id = ?', (user_id,))
            result = c.fetchone()
            username, db_marzban_username, _sub_active, _sub_end_date, _trial_active, _trial_end_date = result if result else (None, None, 0, None, 0, None)

        # Определяем Telegram-ник и имя для проверки в Remnawave
        telegram_username = username.lstrip('@') if username else f"user_{user_id}"
        marzban_username = db_marzban_username if db_marzban_username else telegram_username
        fallback_username = f"user_{user_id}"

        # Токен больше не нужен, так как он передается при инициализации SDK

        msk_tz = pytz.timezone('Europe/Moscow')
        current_time = datetime.now(msk_tz).replace(tzinfo=msk_tz)
        user_info = None
        subscription_url = ""
        status = "❌ Неактивна"
        is_expired = True
        show_details = False
        expire_date = None

        # Шаг 1: Проверяем подписку по Telegram-нику или marzban_username
        logging.info(f"Checking Remnawave for user_id {user_id}, username {marzban_username}")
        user_info = await user_exists(marzban_username)
        if user_info and hasattr(user_info, 'expire_at') and user_info.expire_at:
            expire_date = user_info.expire_at.astimezone(msk_tz)
            logging.info(f"Found user in Remnawave with username {marzban_username}, expire: {expire_date}")
            if expire_date > current_time:
                status = "✅ Активна"
                subscription_url = getattr(user_info, 'subscription_url', '')
                is_expired = False
                show_details = True
            else:
                status = "❌ Истекла"
        else:
            logging.warning(f"No active subscription found in Remnawave for username {marzban_username}")

        # Шаг 2: Если подписка не найдена, проверяем по user_<user_id>
        if not user_info or not (hasattr(user_info, 'expire_at') and user_info.expire_at):
            logging.info(f"Checking Remnawave for fallback username {fallback_username}")
            user_info = await user_exists(fallback_username)
            if user_info and hasattr(user_info, 'expire_at') and user_info.expire_at:
                expire_date = user_info.expire_at.astimezone(msk_tz)
                logging.info(f"Found user in Remnawave with fallback username {fallback_username}, expire: {expire_date}")
                if expire_date > current_time:
                    status = "✅ Активна"
                    subscription_url = getattr(user_info, 'subscription_url', '')
                    is_expired = False
                    show_details = True
                else:
                    status = "❌ Истекла"

                # Синхронизируем marzban_username в базе данных
                with get_db_connection() as conn:
                    c = conn.cursor()
                    c.execute('UPDATE users SET marzban_username = ? WHERE user_id = ?', (fallback_username, user_id))
                    conn.commit()
                    logging.info(f"Updated marzban_username to {fallback_username} for user_id {user_id}")
            else:
                logging.warning(f"No active subscription found in Remnawave for fallback username {fallback_username}")

        # Формируем текст сообщения с тщательным экранированием
        text = f"📋 *Моя подписка*\n\nСтатус: `{escape_markdown_v2(status)}`\n"
        if is_expired:
            text += "\nЧтобы получить доступ\\, купите подписку в главном меню\\!\n\n"
            text += "Если возникла проблема\\, обратитесь в поддержку \\@spacevpn\\_help"
        if show_details and expire_date:
            sub_end_formatted = await format_date(expire_date.isoformat())
            text += f"Окончание: `{escape_markdown_v2(sub_end_formatted)}`\n"
            if user_info and hasattr(user_info, 'used_traffic_bytes'):
                used_traffic = user_info.used_traffic_bytes
                if used_traffic > 0:
                    used_gb = used_traffic / (1024 ** 3)
                    text += f"Использовано: `{used_gb:.2f} GB`\n"
        if not is_expired and subscription_url:
            text += f"\n🔗 Ссылка на подписку:\n>`{escape_markdown_v2(subscription_url)}`\n"

        # Формируем клавиатуру
        keyboard = []
        if not is_expired and subscription_url:
            keyboard.append([
                InlineKeyboardButton("🗂️ Инструкции", callback_data='instructions'),
                InlineKeyboardButton("🔑 Открыть", url=subscription_url)
            ])
        keyboard.append([InlineKeyboardButton("« Назад в профиль", callback_data='profile')])

        try:
            await query.message.edit_caption(
                text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='MarkdownV2'
            )
        except BadRequest as e:
            logging.error(f"MarkdownV2 parsing error: {e}, text: {text}, subscription_url: {subscription_url}")
            # Формируем запасной текст без Markdown
            fallback_text = (
                f"📋 Моя подписка\n\n"
                f"Статус: {status}\n"
            )
            if is_expired:
                fallback_text += "\nЧтобы получить доступ, купите подписку в главном меню!\n\n"
                fallback_text += "Если возникла проблема, обратитесь в поддержку @spacevpn\\_help"
            if show_details and expire_date:
                sub_end_formatted = await format_date(expire_date.isoformat())
                fallback_text += f"Окончание: {sub_end_formatted}\n"
                if user_info and hasattr(user_info, 'used_traffic_bytes'):
                    used_traffic = user_info.used_traffic_bytes
                    if used_traffic > 0:
                        used_gb = used_traffic / (1024 ** 3)
                        fallback_text += f"Использовано: {used_gb:.2f} GB\n"
            if not is_expired and subscription_url:
                fallback_text += f"🔗 Ссылка на подписку: {subscription_url}\n\n"

            await query.message.edit_caption(
                fallback_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=None
            )

    elif query.data.startswith(('purchase_history', 'history_page:')):
        # Получаем текущую страницу
        if query.data.startswith('history_page:'):
            current_page = int(query.data.split(':')[1])
        else:
            current_page = 0  # Первая страница

        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT period, amount, purchase_date, end_date FROM subscription_history WHERE user_id = ? ORDER BY purchase_date DESC', (user_id,))
            subscriptions = c.fetchall()

        if not subscriptions:
            history_text = "📜 *История покупок*\n\nИстория покупок отсутствует"
            await query.message.edit_caption(
                history_text,
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("« Назад в профиль", callback_data='profile')]
                ]),
                parse_mode='MarkdownV2'
            )
            return

        # Пагинация
        items_per_page = 3
        total_pages = (len(subscriptions) + items_per_page - 1) // items_per_page

        # Обрезаем список подписок для текущей страницы
        start_idx = current_page * items_per_page
        end_idx = start_idx + items_per_page
        current_subscriptions = subscriptions[start_idx:end_idx]

        # Формируем текст
        history_text = f"📜 *История покупок*\n\nСтраница {current_page + 1}/{total_pages}\n\n"
        for period, amount, purchase_date, end_date in current_subscriptions:
            purchase_formatted = await format_date(purchase_date)
            end_formatted = await format_date(end_date)
            history_text += (
                f"Тариф: `{escape_markdown_v2(period)}`\n"
                f"Сумма: `{amount}₽`\n"
                f"Дата покупки: `{escape_markdown_v2(purchase_formatted)}`\n"
                f"Окончание: `{escape_markdown_v2(end_formatted)}`\n\n"
            )

        # Создаем клавиатуру с кнопками навигации
        keyboard = []

        # Добавляем кнопки навигации если нужно
        if total_pages > 1:
            nav_buttons = []
            if current_page > 0:
                nav_buttons.append(InlineKeyboardButton("◀️ Назад", callback_data=f'history_page:{current_page - 1}'))
            if current_page < total_pages - 1:
                nav_buttons.append(InlineKeyboardButton("Вперед ▶️", callback_data=f'history_page:{current_page + 1}'))
            keyboard.append(nav_buttons)

        # Добавляем кнопку возврата
        keyboard.append([InlineKeyboardButton("« Назад в профиль", callback_data='profile')])

        await query.message.edit_caption(
            history_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='MarkdownV2'
        )

    elif query.data == 'referral':
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT referral_code FROM users WHERE user_id = ?', (user_id,))
            ref_code = c.fetchone()[0]
            c.execute('SELECT COUNT(*) FROM users WHERE referred_by = ?', (user_id,))
            referral_count = c.fetchone()[0]
        bot_username = (await context.bot.get_me()).username
        referral_link = f"https://t.me/{bot_username}?start={ref_code}"

        # Считаем общую заработанную сумму (включая проценты от покупок)
        total_earned = get_total_referral_earnings(user_id)

        # Текст для отправки
        invite_text = "\n🌟 Приглашаю тебя попробовать SPACE VPN, там дают 5 дней бесплатной подписки"
        invite_text_encoded = quote(invite_text)
        share_url = f"https://t.me/share/url?url={referral_link}&text={invite_text_encoded}"

        await query.message.edit_caption(
        f"👥 *Рефералы*\n\n"
        f"🔗 Твоя реферальная ссылка:\n>`{referral_link}`\n\n"
        f"👥 Приглашено: `{referral_count}`\n"
        f"💰 Заработано: `{total_earned:.2f}₽`\n\n"
        f"Приглашай друзей и получай:\n"
        f"• 25₽ за каждую регистрацию\n"
        f"• 20% от каждой покупки подписки",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("📩 Пригласить друга", url=share_url)],
                [InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]
            ]),
            parse_mode='MarkdownV2'
        )

    elif query.data == 'check_payment_status':
        if user_id in payment_messages:
            message_id = payment_messages[user_id]
            payment_url = payment_messages.get(f"{user_id}_url", "#")
            payment_type = payment_messages.get(f"{user_id}_type", "sbp")
            balance = await get_user_balance(user_id)
            initial_balance = user_states.get(f"{user_id}_initial_balance", balance)
            msk_tz = pytz.timezone('Europe/Moscow')
            timestamp = datetime.now(msk_tz).strftime('%H:%M:%S')
            amount = user_states.get(f"{user_id}_amount", 0)
            currency = "₽" if payment_type == "sbp" or "card" else "USDT"
            if balance > initial_balance:
                text = f"💳 *Пополнение*\n\n✅ *Успешно!*\nБаланс: `{balance:.2f}₽`\nВремя: {timestamp}"
                keyboard = InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
                for key in [user_id, f"{user_id}_url", f"{user_id}_type", f"{user_id}_initial_balance", f"{user_id}_amount"]:
                    if key in payment_messages:
                        del payment_messages[key]
                    if key in user_states:
                        del user_states[key]
            else:
                text = f"💳 *Пополнение*\n\n⏳ Ожидаем `{amount:.2f}{currency}`...\nБаланс: `{balance:.2f}₽`\nВремя: {timestamp}"
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Оплатить", url=payment_url)],
                    [InlineKeyboardButton("✅ Я оплатил", callback_data='payment_verification')],
                    [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                ])
            await context.bot.edit_message_text(chat_id=query.message.chat_id, message_id=message_id, text=text, reply_markup=keyboard, parse_mode='Markdown')

    elif query.data == 'payment_verification':
        # Получаем данные платежа
        if user_id in payment_messages:
            message_id = payment_messages[user_id]
            payment_url = payment_messages.get(f"{user_id}_url", "#")
            payment_type = payment_messages.get(f"{user_id}_type", "sbp")

            # Всегда сбрасываем и заново инициализируем время начала проверки
            msk_tz = pytz.timezone('Europe/Moscow')
            payment_messages[f"{user_id}_verification_start_time"] = datetime.now(msk_tz)
            payment_messages[f"{user_id}_two_minute_warning_shown"] = False
            payment_messages[f"{user_id}_verification_attempts"] = 0
            payment_messages[f"{user_id}_total_wait_time"] = 0

            # Проверяем баланс
            balance = await get_user_balance(user_id)
            initial_balance = user_states.get(f"{user_id}_initial_balance", balance)
            amount = user_states.get(f"{user_id}_amount", 0)
            currency = "₽" if payment_type == "sbp" or "card" else "USDT"

            if balance > initial_balance:
                # Оплата прошла успешно
                timestamp = datetime.now(msk_tz).strftime('%H:%M:%S')
                text = f"💳 *Пополнение*\n\n✅ *Успешно!*\nБаланс: `{balance:.2f}₽`\nВремя: {timestamp}"
                keyboard = InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])

                # Очищаем состояния
                for key in [user_id, f"{user_id}_url", f"{user_id}_type", f"{user_id}_initial_balance", f"{user_id}_amount",
                            f"{user_id}_verification_attempts", f"{user_id}_last_check_time", f"{user_id}_total_wait_time",
                            f"{user_id}_verification_start_time", f"{user_id}_two_minute_warning_shown"]:
                    if key in payment_messages:
                        del payment_messages[key]
                    if key in user_states:
                        del user_states[key]

                await context.bot.edit_message_text(
                    chat_id=query.message.chat_id,
                    message_id=message_id,
                    text=text,
                    reply_markup=keyboard,
                    parse_mode='Markdown'
                )
            else:
                # Оплата не прошла, начинаем автоматические проверки
                timestamp = datetime.now(msk_tz).strftime('%H:%M:%S')

                # Получаем или инициализируем счетчики
                verification_attempts = payment_messages.get(f"{user_id}_verification_attempts", 0) + 1
                payment_messages[f"{user_id}_verification_attempts"] = verification_attempts
                total_wait_time = payment_messages.get(f"{user_id}_total_wait_time", 0)

                # Рассчитываем время ожидания для этой проверки (10, 20, 30... секунд)
                wait_time = 10 * verification_attempts
                payment_messages[f"{user_id}_last_check_time"] = datetime.now(msk_tz)

                # Проверяем, не превышено ли максимальное время ожидания (10 минут)
                if total_wait_time + wait_time > 600:  # 10 минут в секундах
                    text = (
                        f"💳 *Пополнение*\n\n"
                        f"❌ *Платеж не был получен в течение 10 минут*\n\n"
                        f"Если вы уверены, что оплатили, обратитесь в поддержку: @spacevpn\\_help\n"
                        f"Сумма: `{amount:.2f}{currency}`\n"
                        f"Баланс: `{balance:.2f}₽`\n"
                        f"Время: {timestamp}"
                    )
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton("💬 Написать в поддержку", url="https://t.me/spacevpn_help")],
                        [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                    ])

                    # Очищаем состояния
                    for key in [user_id, f"{user_id}_url", f"{user_id}_type", f"{user_id}_initial_balance", f"{user_id}_amount",
                                f"{user_id}_verification_attempts", f"{user_id}_last_check_time", f"{user_id}_total_wait_time",
                                f"{user_id}_verification_start_time", f"{user_id}_two_minute_warning_shown"]:
                        if key in payment_messages:
                            del payment_messages[key]
                        if key in user_states:
                            del user_states[key]
                else:
                    # Обновляем общее время ожидания
                    payment_messages[f"{user_id}_total_wait_time"] = total_wait_time + wait_time

                    # Проверяем, нужно ли показывать предупреждение 2-минутной отметки
                    start_time = payment_messages.get(f"{user_id}_verification_start_time")
                    two_minute_warning_shown = payment_messages.get(f"{user_id}_two_minute_warning_shown", False)
                    elapsed_time = (datetime.now(msk_tz) - start_time).total_seconds() if start_time else 0

                    if elapsed_time >= 120 and not two_minute_warning_shown:  # Прошло 2 минуты и предупреждение еще не показано
                        # Отображаем предупреждение с кнопкой подтверждения
                        payment_messages[f"{user_id}_two_minute_warning_shown"] = True

                        text = (
                            f"💳 *Пополнение*\n\n"
                            f"⚠️ *Внимание!*\n\n"
                            f"Прошло уже 2 минуты, но ваш платеж до сих пор не поступил.\n\n"
                            f"• Вы уверены, что завершили оплату?\n"
                            f"• Проверьте наличие подтверждения от вашего банка\n"
                            f"• Иногда обработка платежа может занимать больше времени\n\n"
                            f"Сумма: `{amount:.2f}{currency}`\n"
                            f"Баланс: `{balance:.2f}₽`\n"
                            f"Время ожидания: `2 мин.`"
                        )

                        keyboard = InlineKeyboardMarkup([
                            [InlineKeyboardButton("Оплатить", url=payment_url)],
                            [InlineKeyboardButton("✅ Я точно оплатил, продолжить проверку", callback_data='confirm_payment_check')],
                            [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                        ])
                    else:
                        # Обычный текст проверки
                        text = (
                            f"💳 *Пополнение*\n\n"
                            f"⏳ *Проверка платежа*\n\n"
                            f"Ваш платеж пока не найден.\n"
                            f"Следующая проверка через {wait_time} секунд.\n\n"
                            f"Сумма: `{amount:.2f}{currency}`\n"
                            f"Баланс: `{balance:.2f}₽`\n"
                            f"Время: {timestamp}"
                        )

                        keyboard = InlineKeyboardMarkup([
                            [InlineKeyboardButton("Оплатить", url=payment_url)],
                            [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                        ])

                    # Запускаем таймер для следующей проверки только если не показываем предупреждение
                    if not (elapsed_time >= 120 and not two_minute_warning_shown):
                        context.job_queue.run_once(
                            check_payment_delayed,
                            wait_time,
                            data={
                                'user_id': user_id,
                                'chat_id': query.message.chat_id,
                                'message_id': message_id
                            }
                        )

                await context.bot.edit_message_text(
                    chat_id=query.message.chat_id,
                    message_id=message_id,
                    text=text,
                    reply_markup=keyboard,
                    parse_mode='Markdown'
                )

    elif query.data == 'confirm_payment_check':
        # Получаем данные платежа
        if user_id in payment_messages:
            message_id = payment_messages[user_id]
            payment_url = payment_messages.get(f"{user_id}_url", "#")
            payment_type = payment_messages.get(f"{user_id}_type", "sbp")

            # Проверяем баланс
            balance = await get_user_balance(user_id)
            initial_balance = user_states.get(f"{user_id}_initial_balance", balance)
            amount = user_states.get(f"{user_id}_amount", 0)
            currency = "₽" if payment_type == "sbp" or "card" else "USDT"

            if balance > initial_balance:
                # Оплата прошла успешно
                msk_tz = pytz.timezone('Europe/Moscow')
                timestamp = datetime.now(msk_tz).strftime('%H:%M:%S')
                text = f"💳 *Пополнение*\n\n✅ *Успешно!*\nБаланс: `{balance:.2f}₽`\nВремя: {timestamp}"
                keyboard = InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])

                # Очищаем состояния
                for key in [user_id, f"{user_id}_url", f"{user_id}_type", f"{user_id}_initial_balance", f"{user_id}_amount",
                            f"{user_id}_verification_attempts", f"{user_id}_last_check_time", f"{user_id}_total_wait_time",
                            f"{user_id}_verification_start_time", f"{user_id}_two_minute_warning_shown"]:
                    if key in payment_messages:
                        del payment_messages[key]
                    if key in user_states:
                        del user_states[key]
            else:
                # Оплата не прошла, продолжаем проверки
                msk_tz = pytz.timezone('Europe/Moscow')
                timestamp = datetime.now(msk_tz).strftime('%H:%M:%S')

                # Получаем или инициализируем счетчики
                verification_attempts = payment_messages.get(f"{user_id}_verification_attempts", 0) + 1
                payment_messages[f"{user_id}_verification_attempts"] = verification_attempts
                total_wait_time = payment_messages.get(f"{user_id}_total_wait_time", 0)

                # Рассчитываем время ожидания для этой проверки (10, 20, 30... секунд)
                wait_time = 10 * verification_attempts
                payment_messages[f"{user_id}_last_check_time"] = datetime.now(msk_tz)

                # Обновляем общее время ожидания
                payment_messages[f"{user_id}_total_wait_time"] = total_wait_time + wait_time

                text = (
                    f"💳 *Пополнение*\n\n"
                    f"⏳ *Проверка платежа продолжается*\n\n"
                    f"Ваш платеж пока не найден.\n"
                    f"Следующая проверка через {wait_time} секунд.\n\n"
                    f"Сумма: `{amount:.2f}{currency}`\n"
                    f"Баланс: `{balance:.2f}₽`\n"
                    f"Время: {timestamp}"
                )

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Оплатить", url=payment_url)],
                    [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                ])

                # Запускаем таймер для следующей проверки
                context.job_queue.run_once(
                    check_payment_delayed,
                    wait_time,
                    data={
                        'user_id': user_id,
                        'chat_id': query.message.chat_id,
                        'message_id': message_id
                    }
                )

            await context.bot.edit_message_text(
                chat_id=query.message.chat_id,
                message_id=message_id,
                text=text,
                reply_markup=keyboard,
                parse_mode='Markdown'
            )

    elif query.data == 'renew_vpn':
        # Отправляем стандартное приветственное сообщение как при команде /start
        await context.bot.send_photo(
            user_id,
            photo="https://i.imgur.com/pN0xMho.png",
            caption="👋 *Добро пожаловать в SPACE VPN\\!*\n\n"
                    "*Наши локации:*\n"
                    "🇫🇮 Финляндия\n"
                    "🇩🇪 Германия\n\n"
                    ">*Ошибка в боте? Нашел баг?*\n"
                    ">Пиши: @spacevpn\\_help\n\n"
                    "👇 *Выберите опцию:*",
            reply_markup=get_main_keyboard(user_id),
            parse_mode='MarkdownV2'
        )

    elif query.data == 'delete_message':
        if user_id in payment_messages and payment_messages[user_id] == query.message.message_id:
            for key in [user_id, f"{user_id}_url", f"{user_id}_type", f"{user_id}_initial_balance", f"{user_id}_amount",
                        f"{user_id}_verification_attempts", f"{user_id}_last_check_time", f"{user_id}_total_wait_time",
                        f"{user_id}_verification_start_time", f"{user_id}_two_minute_warning_shown"]:
                if key in payment_messages:
                    del payment_messages[key]
                if key in user_states:
                    del user_states[key]
            logging.info(f"Payment verification data cleared for user {user_id}")

        await query.message.delete()

    elif query.data == 'instructions':
        # Формируем клавиатуру с кнопками для выбора устройства с прямыми ссылками
        keyboard = [
            [
                InlineKeyboardButton("Android", url="https://telegra.ph/SPACE-VPN-FOR-ANDROID-01-26"),
                InlineKeyboardButton("iOS", url="https://telegra.ph/SPACE-VPN-FOR-IOS-01-26")
            ],
            [
                InlineKeyboardButton("Windows", url="https://telegra.ph/USTANOVKA-VPN-NA-WINDOWS--SPACE-VPN-03-08"),
                InlineKeyboardButton("Linux", url="https://telegra.ph/SPACE-VPN-FOR-LINUX-02-19")
            ],
            [
                InlineKeyboardButton("Android TV", url="https://telegra.ph/SPACE-VPN-FOR-ANDROID-TV-02-11"),
            ],
            [InlineKeyboardButton("« Назад", callback_data='profile')]
        ]

        await query.message.edit_caption(
            "📱 *Инструкции по подключению*\n\n"
            "Выберите ваше устройство для получения инструкций:",
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='MarkdownV2'
        )

    elif query.data == 'check_subscription':
        # Проверяем подписку на канал повторно
        is_subscribed = await check_channel_subscription(context.bot, user_id, REQUIRED_CHANNEL_ID)
        if is_subscribed:
            # Если подписан, показываем кнопку активации пробной подписки
            await query.message.edit_caption(
                "🎁 *Отлично! Вы подписаны на канал*\n\n"
                "Теперь вы можете активировать пробную подписку на 5 дней",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("✅ Активировать", callback_data='activate_trial')],
                    [InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]
                ]),
                parse_mode='Markdown'
            )
        else:
            # Если не подписан, показываем сообщение об ошибке
            await query.message.edit_caption(
                "❌ *Вы еще не подписаны на канал*\n\n"
                "Пожалуйста, подпишитесь на канал @spaacevpn и попробуйте снова",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("📢 Подписаться на канал", url="https://t.me/spaacevpn")],
                    [InlineKeyboardButton("✅ Я подписался, проверить", callback_data='check_subscription')],
                    [InlineKeyboardButton("« Главное меню", callback_data='back_to_main')]
                ]),
                parse_mode='Markdown'
            )

    else:
        await query.answer("Неизвестная команда")