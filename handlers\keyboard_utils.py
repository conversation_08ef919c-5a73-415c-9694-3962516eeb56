from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from database import get_db_connection

def get_main_keyboard(user_id=None):
    # Base keyboard without trial button
    keyboard = [
        [InlineKeyboardButton("📜 Профиль", callback_data='profile')],
        [InlineKeyboardButton("💰 Купить VPN", callback_data='buy')],
        [
            InlineKeyboardButton("👥 Рефералы", callback_data='referral'),
            InlineKeyboardButton("❓ Поддержка", url="https://t.me/spacevpn_help")
        ]
    ]
    
    # Check trial status if user_id is provided
    if user_id:
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT trial_end_date FROM users WHERE user_id = ?', (user_id,))
            trial_data = c.fetchone()
            # Add trial button only if it hasn't been used
            if not trial_data or not trial_data[0]:
                keyboard.insert(0, [InlineKeyboardButton("🎁 Бесплатная подписка (5 дней)", callback_data='trial')])
    
    return InlineKeyboardMarkup(keyboard) 