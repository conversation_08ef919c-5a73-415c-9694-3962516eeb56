import logging
import os
import sys
import threading
from telegram import Update
from telegram.ext import (
    Application,
    CommandHandler,
    CallbackQueryHandler,
    MessageHandler,
    filters,
    JobQueue
)

from config import TELEGRAM_TOKEN
from database import init_db
from payment_utils import app as flask_app
from handlers import (
    start, gift_command, handle_message,
    ref_command, add_balance_command, add_gift_command, check_gift_command,
    announce, userinfo_command, stats_command, reset_balance_command, sync,
    button_handler, check_subscription_expiry, add_days_command, test_api_command
)

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def run_flask():
    flask_app.run(host='0.0.0.0', port=5000, debug=False)

def main():
    # Initialize database
    init_db()
    
    # Start Flask server in a separate thread
    flask_thread = threading.Thread(target=run_flask)
    flask_thread.daemon = True
    flask_thread.start()
    
    # Initialize the bot
    application = Application.builder().token(TELEGRAM_TOKEN).build()
    
    # Add handlers for commands
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("gift", gift_command))
    application.add_handler(CommandHandler("ref", ref_command))
    application.add_handler(CommandHandler("add_balance", add_balance_command))
    application.add_handler(CommandHandler("addgift", add_gift_command))
    application.add_handler(CommandHandler("checkgift", check_gift_command))
    application.add_handler(CommandHandler("announce", announce))
    application.add_handler(CommandHandler("userinfo", userinfo_command))
    application.add_handler(CommandHandler("stats", stats_command))
    application.add_handler(CommandHandler("reset_balance", reset_balance_command))
    application.add_handler(CommandHandler("sync", sync))
    application.add_handler(CommandHandler("add_days", add_days_command))
    application.add_handler(CommandHandler("test_api", test_api_command))
    
    # Add handler for callback queries (button clicks)
    application.add_handler(CallbackQueryHandler(button_handler))
    
    # Add handler for messages
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
    
    # Add job for subscription expiry check
    job_queue = application.job_queue
    job_queue.run_repeating(check_subscription_expiry, interval=3600, first=10)  # Run every hour
    
    # Start the Bot
    application.run_polling()
    
    return 0

if __name__ == '__main__':
    sys.exit(main())