import sqlite3
from contextlib import contextmanager
import logging
from datetime import datetime
import pytz
import aiosqlite  # Добавьте эту зависимость

sqlite3.enable_callback_tracebacks(True)

@contextmanager
def get_db_connection():
    """Создает соединение с базой данных с настройками безопасности"""
    conn = None
    try:
        conn = sqlite3.connect('vpn_bot.db', timeout=10)
        # Настраиваем безопасность
        configure_sqlite_security(conn)
        yield conn
    except Exception as e:
        logging.error(f"Database connection error: {e}")
        if conn:
            conn.rollback()  # Откатываем транзакцию при ошибке
        raise
    finally:
        if conn:
            conn.close()

def configure_sqlite_security(conn):
    """Настраивает параметры безопасности для SQLite соединения"""
    cursor = conn.cursor()
    
    try:
        # Включаем защитный режим (доступно в SQLite 3.26.0+)
        cursor.execute("PRAGMA trusted_schema = OFF")
        cursor.execute("PRAGMA cell_size_check = ON")
        # defensive доступно только в SQLite 3.38.0+
        try:
            cursor.execute("PRAGMA defensive = ON")
        except sqlite3.OperationalError:
            logging.info("PRAGMA defensive not supported in this SQLite version")
        
        # Устанавливаем лимиты для предотвращения DoS атак
        cursor.execute("PRAGMA max_page_count = 5000")
        cursor.execute("PRAGMA cache_size = 2000")
        
        # Используем quick_check вместо integrity_check для скорости
        cursor.execute("PRAGMA quick_check")
        result = cursor.fetchone()
        if result and result[0] != 'ok':
            logging.warning(f"Database quick check warning: {result[0]}")
    except sqlite3.OperationalError as e:
        logging.warning(f"Some security settings not supported: {e}")
    
    logging.info("SQLite security settings configured")

def init_db():
    """Инициализирует базу данных с проверкой целостности"""
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            
            # Проверка целостности только для существующей базы данных
            try:
                c.execute("PRAGMA quick_check")
                integrity_result = c.fetchone()
                if integrity_result and integrity_result[0] != 'ok':
                    logging.warning(f"Database integrity check warning: {integrity_result[0]}")
            except Exception as e:
                logging.warning(f"Could not perform integrity check: {e}")
            
            # Создание таблиц, если они не существуют
            c.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY,
                    username TEXT,
                    marzban_username TEXT,
                    trial_active INTEGER DEFAULT 0,
                    trial_end_date TEXT,
                    subscription_active INTEGER DEFAULT 0,
                    subscription_end_date TEXT,
                    referral_code TEXT,
                    referred_by INTEGER,
                    balance DECIMAL DEFAULT 0.0,
                    expiry_notified INTEGER DEFAULT 0
                )
            ''')
            c.execute('''
                CREATE TABLE IF NOT EXISTS gift_codes (
                    code TEXT PRIMARY KEY,
                    amount DECIMAL,
                    max_users INTEGER,
                    used_users INTEGER DEFAULT 0,
                    created_by INTEGER,
                    created_at TEXT
                )
            ''')
            c.execute('''
                CREATE TABLE IF NOT EXISTS gift_activations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT,
                    user_id INTEGER,
                    activated_at TEXT,
                    FOREIGN KEY (code) REFERENCES gift_codes(code),
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            ''')
            c.execute('''
                CREATE TABLE IF NOT EXISTS payment_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    amount DECIMAL,
                    payment_method TEXT,
                    currency TEXT,
                    timestamp TEXT,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            ''')
            # Новая таблица для истории подписок
            c.execute('''
                CREATE TABLE IF NOT EXISTS subscription_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    period TEXT,  -- Например, "1 месяц", "3 месяца", "1 год"
                    amount DECIMAL,  -- Стоимость подписки
                    purchase_date TEXT,  -- Дата покупки
                    end_date TEXT,  -- Дата окончания подписки
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            ''')

            # Новая таблица для реферальных начислений
            c.execute('''
                CREATE TABLE IF NOT EXISTS referral_earnings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    referrer_id INTEGER,  -- ID пригласившего пользователя
                    referred_id INTEGER,  -- ID приглашенного пользователя
                    amount DECIMAL,  -- Сумма начисления
                    earning_type TEXT,  -- Тип начисления: 'registration' или 'subscription'
                    subscription_amount DECIMAL,  -- Сумма покупки подписки (для типа 'subscription')
                    created_at TEXT,  -- Дата начисления
                    FOREIGN KEY (referrer_id) REFERENCES users(user_id),
                    FOREIGN KEY (referred_id) REFERENCES users(user_id)
                )
            ''')
            c.execute('CREATE INDEX IF NOT EXISTS idx_users_user_id ON users(user_id)')
            c.execute('CREATE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code)')
            c.execute('CREATE INDEX IF NOT EXISTS idx_users_referred_by ON users(referred_by)')
            c.execute('CREATE INDEX IF NOT EXISTS idx_gift_codes_code ON gift_codes(code)')
            c.execute('CREATE INDEX IF NOT EXISTS idx_subscription_history_user_id ON subscription_history(user_id)')
            c.execute('CREATE INDEX IF NOT EXISTS idx_referral_earnings_referrer_id ON referral_earnings(referrer_id)')
            c.execute('CREATE INDEX IF NOT EXISTS idx_referral_earnings_referred_id ON referral_earnings(referred_id)')
            conn.commit()
            c.execute('PRAGMA table_info(users)')
            table_info = c.fetchall()
            logging.info(f"Users table structure: {table_info}")
            logging.info("Database initialized")

    except Exception as e:
        logging.error(f"Error initializing database: {e}")
        # Не выбрасываем исключение, чтобы бот мог продолжить работу

# Новая функция для добавления записи о покупке подписки
def add_subscription_history(user_id, period, amount, end_date):
    # Валидация входных данных
    user_id = validate_input(user_id, "int")
    period = validate_input(period, "string", 50)
    amount = validate_input(amount, "float")
    end_date = validate_input(end_date, "date")
    
    if user_id is None or period is None or amount is None or end_date is None:
        logging.error(f"Invalid input data for subscription history: user_id={user_id}, period={period}, amount={amount}")
        return False
    
    with get_db_connection() as conn:
        c = conn.cursor()
        msk_tz = pytz.timezone('Europe/Moscow')
        purchase_date = datetime.now(msk_tz).isoformat()
        c.execute('''
            INSERT INTO subscription_history (user_id, period, amount, purchase_date, end_date)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, period, amount, purchase_date, end_date))
        conn.commit()
        logging.info(f"Subscription added to history for user {user_id}: {period}, {amount}₽, ends {end_date}")
        return True

def add_balance(user_id, amount, payment_method=None, currency=None):
    """Добавляет баланс пользователю с проверкой входных данных"""
    # Валидация входных данных
    user_id = validate_input(user_id, "int")
    amount = validate_input(amount, "float")
    payment_method = validate_input(payment_method, "string", 50) if payment_method else None
    currency = validate_input(currency, "string", 10) if currency else None
    
    if user_id is None or amount is None:
        logging.error(f"Invalid input data: user_id={user_id}, amount={amount}")
        return False
    
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT balance FROM users WHERE user_id = ?', (user_id,))
            current_balance = c.fetchone()
            if current_balance is None:
                c.execute('INSERT INTO users (user_id, balance) VALUES (?, 0.0)', (user_id,))
                current_amount = 0.0
            else:
                current_amount = float(current_balance[0] or 0.0)
            new_balance = current_amount + amount
            c.execute('UPDATE users SET balance = ? WHERE user_id = ?', (new_balance, user_id))
            
            # Добавляем запись в историю платежей, если указан метод оплаты
            if payment_method and currency:
                import pytz
                msk_tz = pytz.timezone('Europe/Moscow')
                timestamp = datetime.now(msk_tz).isoformat()
                c.execute('INSERT INTO payment_history (user_id, amount, payment_method, currency, timestamp) VALUES (?, ?, ?, ?, ?)',
                          (user_id, amount, payment_method, currency, timestamp))
            
            conn.commit()
            logging.info(f"Balance updated for user {user_id}: {new_balance}")
            return True
    except Exception as e:
        logging.error(f"Error updating balance: {e}")
        return False

async def get_user_balance(user_id):
    """Асинхронно получает баланс пользователя с проверкой входных данных"""
    # Валидация входных данных
    user_id = validate_input(user_id, "int")
    if user_id is None:
        logging.error(f"Invalid user_id in get_user_balance: {user_id}")
        return 0.0
    
    try:    
        async with aiosqlite.connect('vpn_bot.db') as conn:
            # Настраиваем безопасность для асинхронного соединения
            try:
                await conn.execute("PRAGMA trusted_schema = OFF")
            except Exception:
                pass  # Игнорируем, если не поддерживается
            
            async with conn.execute('SELECT balance FROM users WHERE user_id = ?', (user_id,)) as cursor:
                result = await cursor.fetchone()
                return float(result[0]) if result else 0.0
    except Exception as e:
        logging.error(f"Error getting user balance: {e}")
        return 0.0  # Возвращаем 0 в случае ошибки

async def get_or_set_username(user_id, telegram_username=None):
    async with aiosqlite.connect('vpn_bot.db') as conn:
        async with conn.execute('SELECT username, marzban_username FROM users WHERE user_id = ?', (user_id,)) as cursor:
            result = await cursor.fetchone()
            if result:
                current_username, marzban_username = result
                if telegram_username and telegram_username != current_username:
                    await conn.execute('UPDATE users SET username = ? WHERE user_id = ?', (telegram_username, user_id))
                    await conn.commit()
                return telegram_username or current_username, marzban_username or current_username
            else:
                username = telegram_username or f"user_{user_id}"
                marzban_username = username
                await conn.execute(
                    'INSERT OR IGNORE INTO users (user_id, username, marzban_username, referral_code, balance) VALUES (?, ?, ?, ?, 0.0)',
                    (user_id, username, marzban_username, f"ref{user_id}")
                )
                await conn.commit()
                return username, marzban_username

# Функции для работы с реферальными начислениями

def add_referral_earning(referrer_id, referred_id, amount, earning_type, subscription_amount=None):
    """
    Добавляет запись о реферальном начислении

    Args:
        referrer_id: ID пригласившего пользователя
        referred_id: ID приглашенного пользователя
        amount: Сумма начисления
        earning_type: Тип начисления ('registration' или 'subscription')
        subscription_amount: Сумма покупки подписки (для типа 'subscription')
    """
    with get_db_connection() as conn:
        c = conn.cursor()
        msk_tz = pytz.timezone('Europe/Moscow')
        created_at = datetime.now(msk_tz).isoformat()

        c.execute('''
            INSERT INTO referral_earnings (referrer_id, referred_id, amount, earning_type, subscription_amount, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (referrer_id, referred_id, amount, earning_type, subscription_amount, created_at))
        conn.commit()
        logging.info(f"Referral earning added: referrer_id={referrer_id}, referred_id={referred_id}, amount={amount}, type={earning_type}")

def get_total_referral_earnings(user_id):
    """
    Получает общую сумму реферальных начислений пользователя

    Args:
        user_id: ID пользователя

    Returns:
        float: Общая сумма заработанных реферальных бонусов
    """
    with get_db_connection() as conn:
        c = conn.cursor()
        c.execute('SELECT COALESCE(SUM(amount), 0) FROM referral_earnings WHERE referrer_id = ?', (user_id,))
        result = c.fetchone()
        return float(result[0]) if result else 0.0

def process_subscription_referral_bonus(user_id, subscription_amount):
    """
    Обрабатывает начисление реферального бонуса за покупку подписки

    Args:
        user_id: ID пользователя, который купил подписку
        subscription_amount: Сумма покупки подписки

    Returns:
        tuple: (success, referrer_id, bonus_amount) или (False, None, 0) если нет реферера
    """
    with get_db_connection() as conn:
        c = conn.cursor()
        # Проверяем, есть ли у пользователя реферер
        c.execute('SELECT referred_by FROM users WHERE user_id = ?', (user_id,))
        result = c.fetchone()

        if not result or not result[0]:
            return False, None, 0

        referrer_id = result[0]
        bonus_amount = subscription_amount * 0.20  # 20% от суммы покупки

        # Начисляем бонус реферу
        if add_balance(referrer_id, bonus_amount):
            # Записываем информацию о начислении
            add_referral_earning(referrer_id, user_id, bonus_amount, 'subscription', subscription_amount)
            return True, referrer_id, bonus_amount

        return False, referrer_id, bonus_amount

def safe_execute_query(query, params=None, fetch_one=False, fetch_all=False):
    """
    Безопасно выполняет SQL-запрос с проверкой на потенциально опасные операции
    
    Args:
        query: SQL-запрос
        params: Параметры для запроса
        fetch_one: Вернуть одну запись
        fetch_all: Вернуть все записи
        
    Returns:
        Результат запроса или None в случае ошибки
    """
    # Проверка на потенциально опасные операции без WHERE
    query_upper = query.upper()
    
    # Проверяем только запросы, которые могут изменять данные
    if any(keyword in query_upper for keyword in ['DELETE FROM', 'DROP TABLE', 'TRUNCATE']):
        if 'WHERE' not in query_upper and 'LIMIT' not in query_upper:
            logging.warning(f"Potentially dangerous query without WHERE clause: {query}")
            # Не блокируем полностью, но логируем предупреждение
    
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            if params:
                c.execute(query, params)
            else:
                c.execute(query)
                
            if fetch_one:
                return c.fetchone()
            elif fetch_all:
                return c.fetchall()
            else:
                conn.commit()
                return True
    except Exception as e:
        logging.error(f"Error executing query: {e}, Query: {query}")
        if params:
            logging.error(f"Query params: {params}")
        return None

def validate_input(value, input_type="string", max_length=100):
    """
    Проверяет входные данные на безопасность
    
    Args:
        value: Значение для проверки
        input_type: Тип данных ("string", "int", "float", "date")
        max_length: Максимальная длина для строк
        
    Returns:
        Проверенное значение или None, если проверка не пройдена
    """
    if value is None:
        return None
        
    if input_type == "string":
        if not isinstance(value, str):
            logging.warning(f"Input validation failed: expected string, got {type(value)}")
            return None
        if len(value) > max_length:
            logging.warning(f"Input validation failed: string too long ({len(value)} > {max_length})")
            return None
        # Проверяем только на SQL-инъекции в строках, которые могут быть командами
        if any(f" {keyword} " in f" {value.upper()} " for keyword in ['UNION', 'SELECT', 'DROP', 'DELETE', 'INSERT', 'UPDATE', 'EXEC', 'EXECUTE']):
            logging.warning(f"Input validation failed: potentially dangerous string: {value}")
            return None
        return value
        
    elif input_type == "int":
        try:
            return int(value)
        except (ValueError, TypeError):
            logging.warning(f"Input validation failed: cannot convert to int: {value}")
            return None
            
    elif input_type == "float":
        try:
            return float(value)
        except (ValueError, TypeError):
            logging.warning(f"Input validation failed: cannot convert to float: {value}")
            return None
            
    elif input_type == "date":
        if isinstance(value, datetime):
            return value.isoformat()
        if not isinstance(value, str):
            logging.warning(f"Input validation failed: expected date or string, got {type(value)}")
            return None
        try:
            # Пытаемся распарсить дату
            datetime.fromisoformat(value)
            return value
        except ValueError:
            logging.warning(f"Input validation failed: invalid date format: {value}")
            return None
        
    return None
