import logging
import pytz
from datetime import datetime
from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from database import get_user_balance
from handlers.states import user_states, payment_messages

async def check_payment_delayed(context: ContextTypes.DEFAULT_TYPE):
    job_data = context.job.data
    user_id = job_data['user_id']
    chat_id = job_data['chat_id']
    message_id = job_data['message_id']
    
    # Проверяем, актуален ли еще этот платеж
    if user_id not in payment_messages or payment_messages[user_id] != message_id:
        return
    
    # Получаем данные платежа
    payment_type = payment_messages.get(f"{user_id}_type", "sbp")
    payment_url = payment_messages.get(f"{user_id}_url", "#")
    
    # Проверяем баланс
    balance = await get_user_balance(user_id)
    initial_balance = user_states.get(f"{user_id}_initial_balance", balance)
    amount = user_states.get(f"{user_id}_amount", 0)
    currency = "₽" if payment_type == "sbp" or "card" else "USDT"
    
    if balance > initial_balance:
        # Оплата прошла успешно
        msk_tz = pytz.timezone('Europe/Moscow')
        timestamp = datetime.now(msk_tz).strftime('%H:%M:%S')
        text = f"💳 *Пополнение*\n\n✅ *Успешно!*\nБаланс: `{balance:.2f}₽`\nВремя: {timestamp}"
        keyboard = InlineKeyboardMarkup([[InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]])
        
        # Очищаем состояния
        for key in [user_id, f"{user_id}_url", f"{user_id}_type", f"{user_id}_initial_balance", f"{user_id}_amount",
                    f"{user_id}_verification_attempts", f"{user_id}_last_check_time", f"{user_id}_total_wait_time",
                    f"{user_id}_verification_start_time", f"{user_id}_two_minute_warning_shown"]:
            if key in payment_messages:
                del payment_messages[key]
            if key in user_states:
                del user_states[key]
    else:
        # Оплата не прошла, проверяем, нужно ли продолжать автопроверки
        msk_tz = pytz.timezone('Europe/Moscow')
        verification_attempts = payment_messages.get(f"{user_id}_verification_attempts", 0) + 1
        payment_messages[f"{user_id}_verification_attempts"] = verification_attempts
        
        # Получаем время последней проверки и общее время ожидания
        last_check_time = payment_messages.get(f"{user_id}_last_check_time", datetime.now(msk_tz))
        total_wait_time = payment_messages.get(f"{user_id}_total_wait_time", 0)
        
        # Проверяем, нужно ли показывать предупреждение 2-минутной отметки
        start_time = payment_messages.get(f"{user_id}_verification_start_time")
        two_minute_warning_shown = payment_messages.get(f"{user_id}_two_minute_warning_shown", False)
        elapsed_time = (datetime.now(msk_tz) - start_time).total_seconds() if start_time else 0
        
        # Рассчитываем время для следующей проверки
        wait_time = 10 * verification_attempts
        payment_messages[f"{user_id}_last_check_time"] = datetime.now(msk_tz)
        
        # Проверяем, не превышено ли максимальное время ожидания (10 минут)
        if total_wait_time + wait_time > 600:  # 10 минут в секундах
            text = (
                f"💳 *Пополнение*\n\n"
                f"❌ *Платеж не был получен в течение 10 минут*\n\n"
                f"Если вы уверены, что оплатили, обратитесь в поддержку: @spacevpnhelp\n"
                f"Сумма: `{amount:.2f}{currency}`\n"
                f"Баланс: `{balance:.2f}₽`\n"
                f"Время: {datetime.now(msk_tz).strftime('%H:%M:%S')}"
            )
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("💬 Написать в поддержку", url="https://t.me/spacevpnhelp")],
                [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
            ])
            
            # Очищаем состояния
            for key in [user_id, f"{user_id}_url", f"{user_id}_type", f"{user_id}_initial_balance", f"{user_id}_amount",
                        f"{user_id}_verification_attempts", f"{user_id}_last_check_time", f"{user_id}_total_wait_time",
                        f"{user_id}_verification_start_time", f"{user_id}_two_minute_warning_shown"]:
                if key in payment_messages:
                    del payment_messages[key]
                if key in user_states:
                    del user_states[key]
        else:
            # Обновляем общее время ожидания
            payment_messages[f"{user_id}_total_wait_time"] = total_wait_time + wait_time
            
            # Проверяем достижение 2-минутной отметки
            if elapsed_time >= 120 and not two_minute_warning_shown:
                # Показываем предупреждение при достижении 2-минутной отметки
                payment_messages[f"{user_id}_two_minute_warning_shown"] = True
                
                text = (
                    f"💳 *Пополнение*\n\n"
                    f"⚠️ *Внимание!*\n\n"
                    f"Прошло уже 2 минуты, но ваш платеж до сих пор не поступил.\n\n"
                    f"• Вы уверены, что завершили оплату?\n"
                    f"• Проверьте наличие подтверждения от вашего банка\n"
                    f"• Иногда обработка платежа может занимать больше времени\n\n"
                    f"Сумма: `{amount:.2f}{currency}`\n"
                    f"Баланс: `{balance:.2f}₽`\n"
                    f"Время ожидания: `2 мин.`"
                )
                
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Оплатить", url=payment_url)],
                    [InlineKeyboardButton("✅ Я точно оплатил, продолжить проверку", callback_data='confirm_payment_check')],
                    [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                ])
                
                # Не запускаем следующий таймер, пока пользователь не подтвердит
            else:
                text = (
                    f"💳 *Пополнение*\n\n"
                    f"⏳ *Проверка платежа*\n\n"
                    f"Ваш платеж пока не найден.\n"
                    f"Следующая проверка через {wait_time} секунд.\n\n"
                    f"Сумма: `{amount:.2f}{currency}`\n"
                    f"Баланс: `{balance:.2f}₽`\n"
                    f"Время: {datetime.now(msk_tz).strftime('%H:%M:%S')}"
                )
                
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Оплатить", url=payment_url)],
                    [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                ])
                
                # Запускаем таймер для следующей проверки только если не показали 2-минутное предупреждение
                if not (elapsed_time >= 120 and not two_minute_warning_shown):
                    context.job_queue.run_once(
                        check_payment_delayed,
                        wait_time,
                        data={
                            'user_id': user_id,
                            'chat_id': chat_id,
                            'message_id': message_id
                        }
                    )
    
    try:
        await context.bot.edit_message_text(
            chat_id=chat_id,
            message_id=message_id,
            text=text,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )
    except Exception as e:
        logging.error(f"Error updating payment status: {e}")
        # Сообщение могло быть удалено, поэтому просто логгируем ошибку 