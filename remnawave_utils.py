import logging
import os
import asyncio
from datetime import datetime, timedelta
import pytz
from remnawave_api import RemnawaveSDK
from remnawave_api.models.users import CreateUserRequestDto, UpdateUserRequestDto
from database import get_db_connection, add_subscription_history
from dotenv import load_dotenv

load_dotenv()
REMNAWAVE_URL = "https://qwerty.124515.xyz"
REMNAWAVE_TOKEN = os.getenv("REMNAWAVE_TOKEN")

# Поддержка нескольких inbound UUID
# Основной inbound (обязательный)
STEAL_INBOUND_UUID = "b98774f4-9052-476d-bbec-a770ad999149"

# Дополнительные inbound UUID (опциональные)
# Можно добавить через переменные окружения, разделенные запятыми
ADDITIONAL_INBOUND_UUIDS = os.getenv("ADDITIONAL_INBOUND_UUIDS", "").strip()

def get_active_inbound_uuids():
    """
    Возвращает список всех активных inbound UUID

    Returns:
        List[str]: Список UUID inbound'ов для использования
    """
    inbounds = [STEAL_INBOUND_UUID]  # Основной inbound всегда включен

    if ADDITIONAL_INBOUND_UUIDS:
        # Добавляем дополнительные inbound'ы из переменной окружения
        additional = [uuid.strip() for uuid in ADDITIONAL_INBOUND_UUIDS.split(",") if uuid.strip()]
        inbounds.extend(additional)
        logging.info(f"Using additional inbound UUIDs: {additional}")

    logging.info(f"Active inbound UUIDs: {inbounds}")
    return inbounds

# Инициализация SDK клиента
remnawave = RemnawaveSDK(base_url=REMNAWAVE_URL, token=REMNAWAVE_TOKEN)

async def get_remnawave_token():
    """
    Функция для совместимости с предыдущим кодом.
    В новом SDK токен передается при инициализации.
    """
    return REMNAWAVE_TOKEN

async def test_remnawave_connection():
    """
    Тестирует подключение к Remnawave API
    """
    try:
        logging.info("Testing Remnawave API connection...")
        logging.info(f"URL: {REMNAWAVE_URL}")
        logging.info(f"Token available: {bool(REMNAWAVE_TOKEN)}")

        # Пробуем получить список пользователей
        users_response = await remnawave.users.get_all_users_v2(start=0, size=5)
        logging.info(f"API connection successful. Total users: {users_response.total}")
        logging.info(f"First 5 users: {[user.username for user in users_response.users]}")
        return True
    except Exception as e:
        logging.error(f"Failed to connect to Remnawave API: {e}")
        logging.error(f"Exception type: {type(e)}")
        return False

async def test_inbound_configuration():
    """
    Тестирует конфигурацию inbound'ов и проверяет их существование в Remnawave
    """
    try:
        logging.info("Testing inbound configuration...")

        # Получаем список настроенных inbound'ов
        active_inbounds = get_active_inbound_uuids()
        logging.info(f"Configured inbound UUIDs: {active_inbounds}")

        # Получаем список всех inbound'ов из Remnawave
        inbounds_response = await remnawave.inbounds.get_all_inbounds()
        available_inbounds = {str(inbound.uuid): inbound.tag for inbound in inbounds_response.response}

        logging.info(f"Available inbounds in Remnawave: {available_inbounds}")

        # Проверяем каждый настроенный inbound
        valid_inbounds = []
        invalid_inbounds = []

        for inbound_uuid in active_inbounds:
            if inbound_uuid in available_inbounds:
                valid_inbounds.append(f"{inbound_uuid} ({available_inbounds[inbound_uuid]})")
                logging.info(f"✅ Inbound {inbound_uuid} ({available_inbounds[inbound_uuid]}) - OK")
            else:
                invalid_inbounds.append(inbound_uuid)
                logging.error(f"❌ Inbound {inbound_uuid} - NOT FOUND in Remnawave")

        # Результат проверки
        if invalid_inbounds:
            logging.error(f"Invalid inbound UUIDs found: {invalid_inbounds}")
            return False, f"❌ Найдены недействительные inbound UUID: {invalid_inbounds}"
        else:
            logging.info("All configured inbounds are valid")
            return True, f"✅ Все настроенные inbound'ы действительны:\n" + "\n".join([f"- {inbound}" for inbound in valid_inbounds])

    except Exception as e:
        logging.error(f"Error testing inbound configuration: {e}")
        return False, f"❌ Ошибка при проверке конфигурации inbound'ов: {e}"

async def user_exists(username):
    """
    Проверяет существование пользователя в Remnawave

    Args:
        username: Имя пользователя для проверки

    Returns:
        Объект пользователя если пользователь существует, None в противном случае
    """
    if not username:
        logging.error("Username is None or empty in user_exists")
        return None

    try:
        logging.info(f"Checking Remnawave for username: {username}")
        
        # Получаем пользователя по имени
        user = await remnawave.users.get_user_by_username(username)
        logging.info(f"User found in Remnawave: {username}")
        return user
    except Exception as e:
        error_type = type(e).__name__
        error_msg = str(e)
        
        if "404" in error_msg:
            logging.info(f"User {username} not found in Remnawave (404)")
            return None
        elif "401" in error_msg:
            logging.error(f"Authentication error (401) when checking user {username}: {error_msg}")
            # Можно добавить повторную попытку с обновлением токена
            return None
        elif "429" in error_msg:
            logging.error(f"Rate limit exceeded (429) when checking user {username}")
            # Можно добавить задержку и повторную попытку
            await asyncio.sleep(2)
            try:
                user = await remnawave.users.get_user_by_username(username)
                return user
            except Exception:
                logging.error(f"Second attempt failed for user {username}")
                return None
        else:
            logging.error(f"Error checking user existence for {username}: {error_type} - {error_msg}")
            return None

async def activate_trial_subscription(user_id, remnawave_username=None):
    """
    Активирует пробную подписку для пользователя

    Args:
        user_id: ID пользователя Telegram
        remnawave_username: Имя пользователя для Remnawave (опционально)

    Returns:
        Объект пользователя если успешно
    """
    # Проверяем существующий remnawave_username в базе данных
    with get_db_connection() as conn:
        c = conn.cursor()
        c.execute('SELECT marzban_username FROM users WHERE user_id = ?', (user_id,))
        result = c.fetchone()
        db_username = result[0] if result and result[0] else None

    # Используем существующий username, если он есть, иначе user_<user_id>
    remnawave_username = db_username if db_username else f"user_{user_id}"

    logging.info(f"Using remnawave_username: {remnawave_username} for user_id: {user_id}")

    try:
        existing_user = await user_exists(remnawave_username)
        msk_tz = pytz.timezone('Europe/Moscow')
        current_time = datetime.now(msk_tz).replace(tzinfo=msk_tz)
        expire_date = current_time + timedelta(days=5)

        if existing_user:
            # Если пользователь существует, продлеваем подписку
            success, new_expire, updated_user = await extend_remnawave_subscription(user_id, remnawave_username, 5)
            if success:
                logging.info(f"Trial subscription extended for existing user {remnawave_username} (user_id: {user_id})")
                # Записываем в историю подписок
                add_subscription_history(user_id, "Пробная (5 дней)", 0, expire_date)
                logging.info(f"Trial subscription activated for user_id {user_id}, remnawave_username {remnawave_username}")
                return updated_user
            raise Exception(f"Failed to extend subscription: {new_expire}")

        # Создаем нового пользователя
        active_inbounds = get_active_inbound_uuids()
        create_user_dto = CreateUserRequestDto(
            username=remnawave_username,
            expire_at=expire_date.astimezone(pytz.UTC),  # Используем объект datetime с часовым поясом UTC
            traffic_limit_bytes=0,  # Без ограничения трафика
            active_user_inbounds=active_inbounds,  # Добавляем все активные inbound'ы
            telegram_id=user_id  # Добавляем Telegram ID пользователя
        )

        try:
            # Создаем пользователя через API
            added_user = await remnawave.users.create_user(create_user_dto)
        except Exception as create_error:
            # Если пользователь уже существует, пытаемся его найти и обновить
            if "already exists" in str(create_error):
                logging.warning(f"User {remnawave_username} already exists during trial activation, trying to update instead")
                try:
                    # Пытаемся найти пользователя еще раз
                    existing_user = await remnawave.users.get_user_by_username(remnawave_username)
                    if existing_user:
                        # Обновляем существующего пользователя
                        update_user_dto = UpdateUserRequestDto(
                            uuid=existing_user.uuid,
                            expire_at=expire_date.astimezone(pytz.UTC),
                            active_user_inbounds=active_inbounds,
                            telegram_id=user_id
                        )
                        added_user = await remnawave.users.update_user(update_user_dto)
                        logging.info(f"Successfully updated existing user {remnawave_username} for trial")
                    else:
                        raise create_error
                except Exception as update_error:
                    logging.error(f"Failed to update existing user {remnawave_username} for trial: {update_error}")
                    raise create_error
            else:
                raise create_error

        # Обновляем информацию в базе данных
        with get_db_connection() as conn:
            c = conn.cursor()
            # При активации пробной подписки сбрасываем флаг expiry_notified
            c.execute('UPDATE users SET marzban_username = ?, trial_active = 1, trial_end_date = ?, expiry_notified = 0 WHERE user_id = ?',
                      (remnawave_username, expire_date.isoformat(), user_id))
            conn.commit()

        # Добавляем запись в историю подписок для новой пробной подписки
        add_subscription_history(user_id, "Пробная (5 дней)", 0, expire_date)

        logging.info(f"Trial subscription activated for {remnawave_username} (user_id: {user_id})")
        return added_user
    except Exception as e:
        logging.error(f"Error activating trial subscription for user_id {user_id}, remnawave_username {remnawave_username}: {e}")
        raise

async def extend_remnawave_subscription(user_id, remnawave_username, days):
    """
    Продлевает подписку для пользователя

    Args:
        user_id: ID пользователя Telegram
        remnawave_username: Имя пользователя для Remnawave
        days: Количество дней для продления

    Returns:
        Tuple of (success, new_expire_date, user_info)
    """
    # Проверяем существующий remnawave_username в базе данных
    with get_db_connection() as conn:
        c = conn.cursor()
        c.execute('SELECT marzban_username FROM users WHERE user_id = ?', (user_id,))
        result = c.fetchone()
        db_username = result[0] if result and result[0] else None

    # Используем существующий username, если он есть, иначе user_<user_id>
    remnawave_username = db_username if db_username else f"user_{user_id}"

    logging.info(f"Using remnawave_username: {remnawave_username} for user_id: {user_id}")

    try:
        user = await user_exists(remnawave_username)
        msk_tz = pytz.timezone('Europe/Moscow')
        current_time = datetime.now(msk_tz).replace(tzinfo=msk_tz)

        # Определяем новую дату окончания подписки
        if user and hasattr(user, 'expire_at') and user.expire_at and user.expire_at > current_time:
            # Если подписка активна, добавляем дни к текущей дате окончания
            new_expire = user.expire_at.astimezone(msk_tz) + timedelta(days=days)
        else:
            # Если подписка истекла или не существует, начинаем отсчет от текущей даты
            new_expire = current_time.replace(tzinfo=msk_tz) + timedelta(days=days)

        # Дата окончания подписки уже определена в new_expire

        active_inbounds = get_active_inbound_uuids()

        if user:
            # Обновляем существующего пользователя
            update_user_dto = UpdateUserRequestDto(
                uuid=user.uuid,  # UUID пользователя обязателен для обновления
                expire_at=new_expire.astimezone(pytz.UTC),  # Используем объект datetime с часовым поясом UTC
                active_user_inbounds=active_inbounds,  # Добавляем все активные inbound'ы
                telegram_id=user_id  # Добавляем Telegram ID пользователя
            )

            # Обновляем пользователя через API
            updated_user = await remnawave.users.update_user(update_user_dto)
        else:
            # Создаем нового пользователя
            create_user_dto = CreateUserRequestDto(
                username=remnawave_username,
                expire_at=new_expire.astimezone(pytz.UTC),  # Используем объект datetime с часовым поясом UTC
                traffic_limit_bytes=0,  # Без ограничения трафика
                active_user_inbounds=active_inbounds,  # Добавляем все активные inbound'ы
                telegram_id=user_id  # Добавляем Telegram ID пользователя
            )

            try:
                # Создаем пользователя через API
                updated_user = await remnawave.users.create_user(create_user_dto)
            except Exception as create_error:
                # Если пользователь уже существует, пытаемся его найти и обновить
                if "already exists" in str(create_error):
                    logging.warning(f"User {remnawave_username} already exists, trying to update instead")
                    try:
                        # Пытаемся найти пользователя еще раз
                        existing_user = await remnawave.users.get_user_by_username(remnawave_username)
                        if existing_user:
                            # Обновляем существующего пользователя
                            update_user_dto = UpdateUserRequestDto(
                                uuid=existing_user.uuid,
                                expire_at=new_expire.astimezone(pytz.UTC),
                                active_user_inbounds=active_inbounds,
                                telegram_id=user_id
                            )
                            updated_user = await remnawave.users.update_user(update_user_dto)
                            logging.info(f"Successfully updated existing user {remnawave_username}")
                        else:
                            raise create_error
                    except Exception as update_error:
                        logging.error(f"Failed to update existing user {remnawave_username}: {update_error}")
                        raise create_error
                else:
                    raise create_error

        # Обновляем информацию в базе данных
        with get_db_connection() as conn:
            c = conn.cursor()
            # Если подписка продлевается, сбрасываем флаг expiry_notified
            c.execute('UPDATE users SET marzban_username = ?, subscription_active = 1, subscription_end_date = ?, expiry_notified = 0 WHERE user_id = ?',
                      (remnawave_username, new_expire.isoformat(), user_id))
            conn.commit()

        logging.info(f"Subscription updated for {remnawave_username} (user_id: {user_id}) by {days} days")
        return True, new_expire, updated_user
    except Exception as e:
        logging.error(f"Error extending subscription for user_id {user_id}, remnawave_username {remnawave_username}: {e}")
        return False, str(e), None

async def sync_user_with_remnawave(user_id: int) -> str:
    """
    Синхронизирует данные пользователя в базе данных с панелью Remnawave, игнорируя пробную подписку.
    Args:
        user_id: Telegram user_id пользователя для синхронизации.
    Returns:
        str: Сообщение о результате синхронизации.
    """
    # Проверяем тип user_id
    if not isinstance(user_id, int):
        logging.error(f"Invalid user_id type: {type(user_id)}, value: {user_id}")
        return f"❌ Ошибка: user_id должен быть числом, получено: {user_id}"

    # Получаем данные пользователя из базы данных
    try:
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT marzban_username FROM users WHERE user_id = ?', (user_id,))
            result = c.fetchone()
    except Exception as e:
        logging.error(f"Database error while fetching user {user_id}: {e}")
        return f"❌ Ошибка базы данных при получении данных пользователя {user_id}: {e}"

    if not result:
        logging.error(f"User {user_id} not found in database")
        return f"❌ Пользователь с user_id {user_id} не найден в базе данных."

    db_username = result[0]
    if not db_username:
        logging.warning(f"No remnawave_username for user_id {user_id}")
        return f"❌ У пользователя user_id {user_id} не задан username."

    try:
        # Проверяем существование пользователя в Remnawave
        user_info = await user_exists(db_username)
        msk_tz = pytz.timezone('Europe/Moscow')
        current_time = datetime.now(msk_tz).replace(tzinfo=msk_tz)

        with get_db_connection() as conn:
            c = conn.cursor()
            # Сначала получаем текущее значение expiry_notified
            c.execute('SELECT expiry_notified FROM users WHERE user_id = ?', (user_id,))
            result = c.fetchone()
            current_expiry_notified = result[0] if result else 0

            if user_info and hasattr(user_info, 'expire_at') and user_info.expire_at:
                expire_date = user_info.expire_at.astimezone(msk_tz)
                is_active = expire_date > current_time

                # Устанавливаем параметры подписки
                subscription_active = int(is_active)
                subscription_end_date = expire_date.isoformat() if is_active else None
                trial_active = 0  # Пробная подписка всегда отключена
                trial_end_date = None  # Пробная дата всегда NULL
                # Сохраняем текущее значение expiry_notified
                expiry_notified = current_expiry_notified

                # Обновляем данные в базе
                c.execute('''
                    UPDATE users SET
                        subscription_active = ?,
                        subscription_end_date = ?,
                        trial_active = ?,
                        trial_end_date = ?,
                        expiry_notified = ?
                    WHERE user_id = ?
                ''', (subscription_active, subscription_end_date, trial_active, trial_end_date, expiry_notified, user_id))
                conn.commit()

                logging.info(f"Synced user {user_id}: remnawave_username={db_username}, "
                            f"subscription_active={subscription_active}, trial_active={trial_active}, "
                            f"end_date={expire_date.isoformat() if is_active else 'None'}")
                return (f"✅ Пользователь user_id {user_id} синхронизирован:\n"
                        f"Remnawave username: {db_username}\n"
                        f"Подписка активна: {'да' if subscription_active else 'нет'}\n"
                        f"Дата окончания: {expire_date.strftime('%d.%m.%Y %H:%M') if is_active else 'отсутствует'}")
            else:
                # Пользователь не найден или подписка истекла
                # Сохраняем текущее значение expiry_notified
                c.execute('SELECT expiry_notified FROM users WHERE user_id = ?', (user_id,))
                result = c.fetchone()
                current_expiry_notified = result[0] if result else 0

                c.execute('''
                    UPDATE users SET
                        subscription_active = 0,
                        subscription_end_date = NULL,
                        trial_active = 0,
                        trial_end_date = NULL,
                        expiry_notified = ?
                    WHERE user_id = ?
                ''', (current_expiry_notified, user_id,))
                conn.commit()

                logging.info(f"Synced user {user_id}: remnawave_username={db_username}, no active subscription in Remnawave")
                return (f"✅ Пользователь user_id {user_id} синхронизирован:\n"
                        f"Remnawave username: {db_username}\n"
                        f"Подписка активна: нет\n"
                        f"Дата окончания: отсутствует")
    except Exception as e:
        logging.error(f"Error syncing user {user_id} with Remnawave: {e}")
        return f"❌ Ошибка при синхронизации user_id {user_id}: {e}"

