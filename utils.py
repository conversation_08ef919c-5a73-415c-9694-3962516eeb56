from datetime import datetime
import pytz
import logging
from telegram import Bo<PERSON>
from telegram.error import TelegramError

def escape_markdown_v2(text):
    if text is None:
        return ''
    text = str(text)
    # Экранируем все зарезервированные символы MarkdownV2, включая точку
    reserved_chars = r'_*[]()~`>#+=|{}.!-'
    for char in reserved_chars:
        text = text.replace(char, f'\\{char}')
    return text

async def format_date(date_str):
    if not date_str:
        return 'Нет'
    try:
        date = datetime.fromisoformat(date_str)
        if date.tzinfo is None:
            date = pytz.UTC.localize(date)
        msk_tz = pytz.timezone('Europe/Moscow')
        return date.astimezone(msk_tz).strftime('%d.%m.%Y %H:%M')
    except Exception:
        return 'Нет'

async def check_channel_subscription(bot: Bo<PERSON>, user_id: int, channel_id: str) -> bool:
    """
    Проверяет, подписан ли пользователь на указанный канал

    Args:
        bot: Экземпляр бота Telegram
        user_id: ID пользователя Telegram
        channel_id: ID канала (например, "@spaacevpn")

    Returns:
        bool: True если пользователь подписан, False если нет
    """
    try:
        member = await bot.get_chat_member(chat_id=channel_id, user_id=user_id)
        # Проверяем статус участника канала
        # member, administrator, creator - подписан
        # left, kicked - не подписан
        return member.status in ['member', 'administrator', 'creator']
    except TelegramError as e:
        logging.error(f"Error checking channel subscription for user {user_id} in channel {channel_id}: {e}")
        # В случае ошибки (например, бот не админ канала) возвращаем False
        return False
    except Exception as e:
        logging.error(f"Unexpected error checking channel subscription: {e}")
        return False