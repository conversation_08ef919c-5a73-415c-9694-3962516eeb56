import logging
import asyncio
import pytz
from datetime import datetime, timed<PERSON>ta
from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from database import get_db_connection
from remnawave_utils import user_exists
from utils import format_date

async def check_subscription_expiry(context: ContextTypes.DEFAULT_TYPE):
    with get_db_connection() as conn:
        c = conn.cursor()
        c.execute('SELECT user_id, marzban_username, subscription_active, subscription_end_date, trial_active, trial_end_date, expiry_notified FROM users WHERE subscription_active = 1 OR trial_active = 1')
        users = c.fetchall()

    logging.info(f"Found {len(users)} active subscriptions in DB: {users}")
    if not users:
        logging.info("No active subscriptions found in database, checking Marzban...")
        with get_db_connection() as conn:
            c = conn.cursor()
            c.execute('SELECT user_id, marzban_username, expiry_notified FROM users')
            all_users = c.fetchall()
        # Preserve the expiry_notified flag when creating the list of users to check
        users = [(user_id, marzban_username, 0, None, 0, None, expiry_notified) for user_id, marzban_username, expiry_notified in all_users if marzban_username]

    msk_tz = pytz.timezone('Europe/Moscow')
    current_time = datetime.now(msk_tz)
    # Токен больше не нужен, так как он передается при инициализации SDK
    semaphore = asyncio.Semaphore(30)

    async def check_user(user_id, marzban_username, sub_active, sub_end_date, trial_active, trial_end_date, expiry_notified):
        async with semaphore:
            try:
                user_info = await user_exists(marzban_username) if marzban_username else None
                if user_info and user_info.expire_at:
                    end_date = user_info.expire_at.astimezone(msk_tz)
                    if user_info.expire_at > current_time.replace(tzinfo=pytz.UTC):
                        with get_db_connection() as conn:
                            c = conn.cursor()
                            if trial_active:
                                c.execute('UPDATE users SET trial_end_date = ?, trial_active = 1 WHERE user_id = ?', (end_date.isoformat(), user_id))
                            else:
                                c.execute('UPDATE users SET subscription_end_date = ?, subscription_active = 1 WHERE user_id = ?', (end_date.isoformat(), user_id))
                            conn.commit()
                    else:
                        with get_db_connection() as conn:
                            c = conn.cursor()
                            c.execute('UPDATE users SET subscription_active = 0, trial_active = 0 WHERE user_id = ?', (user_id,))
                            conn.commit()
                else:
                    end_date = (
                        datetime.fromisoformat(sub_end_date or trial_end_date).astimezone(msk_tz)
                        if (sub_end_date or trial_end_date)
                        else None
                    )

                if not end_date:
                    logging.warning(f"No valid end date for user {user_id}")
                    return

                time_left = (end_date - current_time).total_seconds() / 3600

                if 24 < time_left <= 72 and not expiry_notified:
                    await context.bot.send_message(
                        user_id,
                        f"⚠️ *Подписка скоро закончится!*\n\nДата окончания: `{end_date.strftime('%d.%m.%Y %H:%M')}`\nОсталось: `{time_left:.1f}` часов\n\nПродли подписку в главном меню — Купить VPN",
                        parse_mode='Markdown',
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton("💰 Продлить VPN", callback_data='renew_vpn')],
                            [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                        ])
                    )
                    with get_db_connection() as conn:
                        c = conn.cursor()
                        c.execute('UPDATE users SET expiry_notified = 1 WHERE user_id = ?', (user_id,))
                        conn.commit()
                    logging.info(f"Sent 3-day warning to user {user_id}")

                elif 0 < time_left <= 24 and not expiry_notified:
                    await context.bot.send_message(
                        user_id,
                        f"⚠️ *Подписка заканчивается!*\n\nДата окончания: `{end_date.strftime('%d.%m.%Y %H:%M')}`\nОсталось: `{time_left:.1f}` часов\n\nНе забудь продлить подписку!",
                        parse_mode='Markdown',
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton("💰 Продлить VPN", callback_data='renew_vpn')],
                            [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                        ])
                    )
                    with get_db_connection() as conn:
                        c = conn.cursor()
                        c.execute('UPDATE users SET expiry_notified = 1 WHERE user_id = ?', (user_id,))
                        conn.commit()
                    logging.info(f"Sent 24-hour warning to user {user_id}")

                elif time_left <= 0 and not expiry_notified:
                    await context.bot.send_message(
                        user_id,
                        f"❌ *Подписка истекла!*\n\nДата окончания: `{end_date.strftime('%d.%m.%Y %H:%M')}`\n\nПродли подписку в главном меню — Купить VPN",
                        parse_mode='Markdown',
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton("💰 Продлить VPN", callback_data='renew_vpn')],
                            [InlineKeyboardButton("🗑️ Удалить", callback_data='delete_message')]
                        ])
                    )
                    with get_db_connection() as conn:
                        c = conn.cursor()
                        c.execute('UPDATE users SET subscription_active = 0, trial_active = 0, expiry_notified = 1 WHERE user_id = ?', (user_id,))
                        conn.commit()
                    logging.info(f"Sent expiry notification to user {user_id}")

            except Exception as e:
                logging.error(f"Error checking subscription for user {user_id}: {e}", exc_info=True)

    await asyncio.gather(*(check_user(*user) for user in users))
    logging.info(f"Subscription expiry check completed for {len(users)} users.")